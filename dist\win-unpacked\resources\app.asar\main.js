/**
 * 主进程文件 - 负责创建窗口、控制应用生命周期
 * 
 * 功能：
 * 1. 创建固定大小的无边框窗口(1920x1080)
 * 2. 设置预加载脚本并启用Node.js集成
 * 3. 管理应用生命周期事件
 */

const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

// 保持对窗口对象的全局引用，避免JavaScript对象被垃圾回收时窗口关闭
let mainWindow;

/**
 * 创建主窗口函数
 */
function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1920,
    height: 1080,
    frame: false, // 隐藏默认标题栏
    resizable: false, // 禁止调整窗口大小
    webPreferences: {
      nodeIntegration: false, // 禁用直接集成，使用预加载脚本
      contextIsolation: true, // 启用上下文隔离
      preload: path.join(__dirname, 'preload.js'), // 预加载脚本
      enableRemoteModule: false // 禁用remote模块
    }
  });

  // 加载应用的index.html
  mainWindow.loadFile('index.html');

  // 窗口关闭时触发
  mainWindow.on('closed', function () {
    // 取消引用窗口对象
    mainWindow = null;
  });
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(() => {
  createWindow();

  // 在macOS上，当所有窗口都被关闭时，通常会重新创建一个窗口
  app.on('activate', function () {
    if (mainWindow === null) createWindow();
  });
});

// 处理窗口关闭事件
ipcMain.on('close-window', () => {
  console.log("收到关闭窗口请求");
  if (mainWindow) {
    mainWindow.close();
  }
});

// 处理窗口最小化事件
ipcMain.on('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

// 处理标题栏拖拽
ipcMain.on('drag-window', () => {
  if (mainWindow) {
    mainWindow.startWindowDrag(); // Electron 14+提供的原生拖拽方法
  }
});

// 除了macOS外，当所有窗口都被关闭的时候退出程序
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
}); 