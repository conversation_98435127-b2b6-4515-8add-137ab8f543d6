<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>调试页面</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
    }
    button {
      padding: 10px 20px;
      margin: 10px;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <h1>窗口操作调试</h1>
  
  <button id="close-btn">关闭窗口</button>
  <button id="minimize-btn">最小化窗口</button>
  <button id="drag-btn">模拟拖拽</button>
  
  <div id="log" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; max-height: 300px; overflow: auto;"></div>
  
  <script>
    // 日志函数
    function log(message) {
      const logElement = document.getElementById('log');
      const logEntry = document.createElement('div');
      logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
      logElement.appendChild(logEntry);
      console.log(message);
    }
    
    // 初始化时检查API
    document.addEventListener('DOMContentLoaded', () => {
      log('页面加载完成');
      log(`electronAPI存在: ${typeof window.electronAPI !== 'undefined'}`);
      if (window.electronAPI) {
        log(`closeWindow方法存在: ${typeof window.electronAPI.closeWindow === 'function'}`);
        log(`minimizeWindow方法存在: ${typeof window.electronAPI.minimizeWindow === 'function'}`);
        log(`dragWindow方法存在: ${typeof window.electronAPI.dragWindow === 'function'}`);
      }
    });
    
    // 关闭按钮
    document.getElementById('close-btn').addEventListener('click', () => {
      log('点击关闭按钮');
      try {
        if (window.electronAPI && typeof window.electronAPI.closeWindow === 'function') {
          window.electronAPI.closeWindow();
          log('已发送关闭窗口请求');
        } else {
          log('错误: electronAPI.closeWindow不存在或不是函数');
        }
      } catch (error) {
        log(`错误: ${error.message}`);
      }
    });
    
    // 最小化按钮
    document.getElementById('minimize-btn').addEventListener('click', () => {
      log('点击最小化按钮');
      try {
        if (window.electronAPI && typeof window.electronAPI.minimizeWindow === 'function') {
          window.electronAPI.minimizeWindow();
          log('已发送最小化窗口请求');
        } else {
          log('错误: electronAPI.minimizeWindow不存在或不是函数');
        }
      } catch (error) {
        log(`错误: ${error.message}`);
      }
    });
    
    // 拖拽按钮
    document.getElementById('drag-btn').addEventListener('click', () => {
      log('点击拖拽按钮');
      try {
        if (window.electronAPI && typeof window.electronAPI.dragWindow === 'function') {
          window.electronAPI.dragWindow();
          log('已发送拖拽窗口请求');
        } else {
          log('错误: electronAPI.dragWindow不存在或不是函数');
        }
      } catch (error) {
        log(`错误: ${error.message}`);
      }
    });
  </script>
</body>
</html> 