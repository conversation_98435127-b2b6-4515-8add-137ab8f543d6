/**
 * 主样式文件 - 定义应用的全局样式和主题
 * 
 * 包含：
 * 1. 全局样式重置
 * 2. 标题栏样式
 * 3. 内容区域样式
 * 4. 版本信息样式
 */

/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  user-select: none; /* 防止文本选择影响拖拽 */
}

body {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  overflow: hidden;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 自定义标题栏样式 */
.title-bar {
  height: 40px;
  background-color: #2c3e50;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  -webkit-app-region: drag; /* 启用标题栏拖拽 */
  color: white;
}

.app-title {
  font-size: 14px;
  font-weight: 500;
}

.window-controls {
  display: flex;
  -webkit-app-region: no-drag; /* 按钮区域不可拖拽 */
}

.window-control-button {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  margin-left: 2px; /* 按钮之间的间距 */
}

.window-control-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.minimize-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.maximize-button {
  opacity: 0.5;
  cursor: not-allowed !important;
}

.maximize-button:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

/* 按钮抖动效果 */
@keyframes shake {
  0% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  50% { transform: translateX(2px); }
  75% { transform: translateX(-2px); }
  100% { transform: translateX(0); }
}

.shake {
  animation: shake 0.4s ease-in-out;
}

.close-button:hover {
  background-color: #e74c3c;
}

/* 内容区域样式 - 移除滚动功能 */
.content-area {
  flex: 1;
  background-color: #ffffff;
  overflow: hidden; /* 禁用滚动 */
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.content-message {
  font-size: 24px;
  margin-bottom: 20px;
}

/* 版本信息样式 */
.version-info {
  margin-top: 30px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #3498db;
  font-size: 14px;
}

.version-info h3 {
  margin-bottom: 10px;
  color: #2c3e50;
}

.version-info p {
  margin: 5px 0;
  color: #34495e;
} 