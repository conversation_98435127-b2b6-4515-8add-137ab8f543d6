/**
 * 海盗王Online游戏指南 - 主样式文件
 */

/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Noto Sans SC', 'Microsoft YaHei', Arial, sans-serif;
  overflow: hidden;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

.app-container {
  display: flex;
  width: 100%;
  height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
  width: 200px;
  background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
  border-right: 2px solid #0f3460;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.sidebar-header {
  padding: 20px 15px;
  text-align: center;
  border-bottom: 1px solid #0f3460;
  background: linear-gradient(135deg, #0f3460 0%, #1a1a2e 100%);
}

.logo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-bottom: 10px;
  border: 3px solid #ffd700;
  object-fit: cover;
}

.sidebar-header h1 {
  color: #ffd700;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 5px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.sidebar-header p {
  color: #87ceeb;
  font-size: 12px;
  opacity: 0.8;
}

/* 导航菜单样式 */
.nav-menu {
  list-style: none;
  padding: 10px 0;
  flex: 1;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  color: #b0c4de;
}

.nav-item:hover {
  background: linear-gradient(90deg, rgba(255, 215, 0, 0.1) 0%, transparent 100%);
  border-left-color: #ffd700;
  color: #ffd700;
}

.nav-item.active {
  background: linear-gradient(90deg, rgba(255, 215, 0, 0.2) 0%, transparent 100%);
  border-left-color: #ffd700;
  color: #ffd700;
  font-weight: 500;
}

.nav-item .icon {
  margin-right: 10px;
  font-size: 16px;
}

.nav-item span {
  font-size: 14px;
}

/* 主内容区域样式 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  overflow: hidden;
}

.content-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
}

.content-header h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%);
}

.content-body {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%);
}

/* 内容区域样式 */
.content-section {
  display: none;
}

.content-section.active {
  display: block;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 功能卡片网格 */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 10px;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e1e8ed;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 32px;
  margin-bottom: 15px;
  display: inline-block;
}

.feature-card h3 {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
}

.feature-card p {
  color: #5a6c7d;
  font-size: 14px;
  line-height: 1.6;
}

/* 装备升级流程样式 */
.equipment-container {
  max-width: 600px;
  margin: 0 auto;
}

.equipment-intro {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.equipment-intro h3 {
  color: #2c3e50;
  font-size: 20px;
  margin-bottom: 10px;
}

.upgrade-flow {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.upgrade-step {
  display: flex;
  align-items: center;
  background: white;
  padding: 15px 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  transition: transform 0.3s ease;
}

.upgrade-step:hover {
  transform: translateX(10px);
}

.step-number {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 15px;
  flex-shrink: 0;
}

.step-content h4 {
  color: #2c3e50;
  font-size: 16px;
  margin-bottom: 5px;
}

.step-content p {
  color: #5a6c7d;
  font-size: 14px;
  margin: 0;
}

.upgrade-arrow {
  font-size: 24px;
  color: #667eea;
  margin: 5px 0;
}

/* 滚动条样式 */
.content-body::-webkit-scrollbar {
  width: 8px;
}

.content-body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.content-body::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

.content-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #5a67d8 0%, #6b46c1 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feature-grid {
    grid-template-columns: 1fr;
  }

  .sidebar {
    width: 180px;
  }

  .content-header {
    padding: 15px 20px;
  }

  .content-header h2 {
    font-size: 20px;
  }

  .upgrade-step {
    max-width: 100%;
  }
}