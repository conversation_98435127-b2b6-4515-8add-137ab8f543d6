{"name": "pirate-king-online-guide", "version": "1.0.0", "description": "海盗王Online游戏指南 - 大坪社区专用桌面应用程序", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux"}, "author": "", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.6.4"}, "build": {"appId": "com.daping.pirate-king-guide", "productName": "海盗王Online指南", "directories": {"output": "dist"}, "files": ["**/*", "!dist/**/*", "!node_modules/**/*"], "win": {"target": "nsis"}, "mac": {"target": "dmg"}, "linux": {"target": "AppImage"}}}