{"name": "electron-desktop-app", "version": "1.0.0", "description": "Electron桌面应用程序，具有固定的1920x1080像素窗口大小，自定义标题栏和简洁的内容区域", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux"}, "author": "", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.6.4"}, "build": {"appId": "com.example.electron-desktop-app", "productName": "Electron Desktop App", "directories": {"output": "dist"}, "files": ["**/*", "!dist/**/*", "!node_modules/**/*"], "win": {"target": "nsis"}, "mac": {"target": "dmg"}, "linux": {"target": "AppImage"}}}