/**
 * 预加载脚本 - 安全地将Node.js和Electron API暴露给渲染进程
 * 
 * 功能：
 * 1. 通过上下文隔离环境安全地暴露必要的API
 * 2. 提供渲染进程与主进程通信的接口
 */

const { contextBridge, ipcRenderer } = require('electron');

// 在window对象上暴露API，供渲染进程使用
contextBridge.exposeInMainWorld('electronAPI', {
  // 窗口控制
  closeWindow: () => ipcRenderer.send('close-window'),
  minimizeWindow: () => ipcRenderer.send('minimize-window'),
  
  // 版本信息
  getVersions: () => process.versions,
  
  // 拖拽支持 - 使用更简单直接的方式
  dragWindow: () => ipcRenderer.send('drag-window')
}); 