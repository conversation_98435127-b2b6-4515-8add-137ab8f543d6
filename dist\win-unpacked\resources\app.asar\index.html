<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Electron桌面应用</title>
  <link rel="stylesheet" href="styles/main.css">
  <style>
    /* 增强关闭按钮的可见性和可点击区域 */
    .close-button {
      background-color: rgba(255, 255, 255, 0.1);
      transition: background-color 0.2s;
    }
    
    .close-button:hover {
      background-color: #e74c3c !important;
    }
    
    .minimize-button:hover {
      background-color: rgba(255, 255, 255, 0.2) !important;
    }
    
    .maximize-button {
      opacity: 0.5;
      cursor: not-allowed !important;
    }
    
    .window-control-button svg {
      width: 14px;
      height: 14px;
    }
  </style>
</head>
<body>
  <!-- 自定义标题栏 -->
  <div class="title-bar">
    <div class="app-title">Electron桌面应用</div>
    <div class="window-controls">
      <div class="window-control-button minimize-button" id="minimize-button">
        <svg width="14" height="14" viewBox="0 0 12 12">
          <rect fill="currentColor" width="10" height="1" x="1" y="6"></rect>
        </svg>
      </div>
      <div class="window-control-button maximize-button" id="maximize-button">
        <svg width="14" height="14" viewBox="0 0 12 12">
          <rect fill="currentColor" width="9" height="9" x="1.5" y="1.5" style="fill:none;stroke:currentColor;stroke-width:1.2"></rect>
        </svg>
      </div>
      <div class="window-control-button close-button" id="close-button">
        <svg width="14" height="14" viewBox="0 0 12 12">
          <path fill="currentColor" d="M6 5.293l4.146-4.147a.5.5 0 0 1 .708.708L6.707 6l4.147 4.146a.5.5 0 0 1-.708.708L6 6.707l-4.146 4.147a.5.5 0 0 1-.708-.708L5.293 6 1.146 1.854a.5.5 0 1 1 .708-.708L6 5.293z"/>
        </svg>
      </div>
    </div>
  </div>
  
  <!-- 内容区域 -->
  <div class="content-area">
    <h1 class="content-message">Hello Electron!</h1>
    <p>这是一个具有1920x1080像素固定大小的Electron桌面应用程序。</p>
    <p>它具有自定义标题栏和内容区域，并且支持窗口拖拽移动。</p>
    <!-- 版本信息将在这里动态添加 -->
  </div>

  <!-- 引入模块化的渲染进程脚本 -->
  <script src="modules/titlebar.js"></script>
  <script src="modules/content.js"></script>
</body>
</html> 