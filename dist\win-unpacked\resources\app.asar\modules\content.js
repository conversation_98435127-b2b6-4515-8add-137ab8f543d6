/**
 * 内容区域模块 - 处理内容区域的交互和功能
 * 
 * 功能：
 * 1. 展示应用的版本信息
 * 2. 可扩展的内容区域功能
 */

// 获取内容区域元素
const contentArea = document.querySelector('.content-area');

// 添加版本信息到内容区域
function displayVersionInfo() {
  const versions = window.electronAPI.getVersions();
  
  const versionInfo = document.createElement('div');
  versionInfo.className = 'version-info';
  versionInfo.style.marginTop = '30px';
  versionInfo.style.padding = '10px';
  versionInfo.style.backgroundColor = '#f8f9fa';
  versionInfo.style.borderRadius = '4px';
  
  versionInfo.innerHTML = `
    <h3 style="margin-bottom: 10px;">系统信息</h3>
    <p>Electron: v${versions.electron}</p>
    <p>Chrome: v${versions.chrome}</p>
    <p>Node.js: v${versions.node}</p>
    <p>V8: v${versions.v8}</p>
  `;
  
  contentArea.appendChild(versionInfo);
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
  displayVersionInfo();
}); 