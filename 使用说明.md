# 海盗王Online游戏指南 - 使用说明

## 应用概述

这是一个专为大坪社区海盗王Online服务器设计的桌面游戏指南应用。应用采用Electron框架开发，提供了美观的用户界面和丰富的游戏信息。

## 应用特性

### 窗口设置
- **窗口尺寸**: 固定900x550像素
- **边框**: 使用系统标题栏和边框
- **不可调整大小**: 保持固定尺寸以确保最佳显示效果

### 界面设计
- **侧边栏导航**: 200像素宽度的深色主题侧边栏
- **游戏主题**: 蓝色渐变背景，金色强调色
- **响应式卡片**: 自适应网格布局的功能卡片
- **平滑动画**: 页面切换和悬停效果

### 功能模块

1. **基础设置**
   - 角色等级系统
   - 装备系统概述
   - 掉落系统介绍
   - 游戏设置选项
   - 挂机系统
   - 背包系统
   - 召唤系统
   - 宝石系统

2. **装备系统**
   - 装备升级流程图
   - 武器升级路径
   - 装备强化系统

3. **怪物掉落**
   - 掉落机制说明
   - 特殊物品介绍

4. **副本介绍**
   - 经典副本
   - 特色副本

5. **觉醒功能**
   - 觉醒条件
   - 觉醒效果

6. **恶魔世界**
   - 恶魔BOSS
   - 特殊奖励

7. **遗失岛屿**
   - 探索奖励
   - 隐藏任务

## 运行方式

### 开发模式
```bash
npm start
```

### 构建应用
```bash
# 构建Windows版本
npm run build:win

# 构建所有平台
npm run build
```

## 技术栈

- **框架**: Electron 28.0.0
- **构建工具**: electron-builder 24.6.4
- **样式**: 原生CSS3，支持现代浏览器特性
- **字体**: Noto Sans SC (中文优化)

## 文件结构

```
├── main.js              # Electron主进程
├── index.html           # 主界面HTML
├── styles/
│   └── main.css         # 主样式文件
├── modules/
│   └── content.js       # 内容管理模块
├── 图片/                # 游戏资源图片
├── package.json         # 项目配置
└── 使用说明.md          # 本文档
```

## 自定义和扩展

### 添加新内容
1. 在 `modules/content.js` 的 `gameGuideData` 对象中添加新的内容模块
2. 在 `index.html` 中添加对应的导航项和内容区域
3. 在 `styles/main.css` 中添加相应的样式

### 修改样式
- 主要颜色变量在CSS文件顶部定义
- 响应式断点设置为768px
- 支持自定义滚动条样式

### 更新游戏资源
- 将新的游戏图片放入 `图片/` 目录
- 在HTML和CSS中引用新的图片路径

## 注意事项

1. **图片资源**: 确保 `图片/1.jpg` 存在，作为应用Logo使用
2. **字体加载**: 应用使用Google Fonts，需要网络连接
3. **兼容性**: 基于Electron 28.0.0，支持现代Web标准
4. **性能**: 应用针对900x550分辨率优化，在其他尺寸下可能需要调整

## 更新日志

### v1.0.0 (2024-08-02)
- 初始版本发布
- 完整的游戏指南内容
- 美观的用户界面
- 响应式设计
- 装备升级流程图

## 技术支持

如需技术支持或功能建议，请联系大坪社区管理员。
