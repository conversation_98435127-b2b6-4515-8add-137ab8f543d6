# Electron桌面应用程序

## 项目简介

这是一个基于Electron 28+的桌面应用程序，具有固定的1920x1080像素窗口大小，自定义标题栏和简洁的内容区域。项目采用模块化架构设计，包含完整的开发和打包配置。

## 功能特点

1. **窗口配置**
   - 尺寸固定为1920x1080像素
   - 隐藏默认标题栏
   - 预加载脚本启用Node.js集成
   - 支持应用打包

2. **自定义标题栏**
   - 高度40px，深色背景(#2c3e50)
   - 左侧显示应用名称
   - 右侧包含最小化、禁用的最大化和关闭按钮
   - 支持窗口拖拽移动

3. **内容区域**
   - 白色背景
   - 居中布局显示内容
   - 示例文本和版本信息

## 项目结构

```
├── main.js                # 主进程文件
├── preload.js             # 预加载脚本
├── index.html             # 渲染进程主页面
├── package.json           # 项目配置文件
├── .npmrc                 # NPM配置文件（阿里云镜像）
├── styles/                # 样式文件目录
│   └── main.css           # 主样式文件
└── modules/               # 模块化组件目录
    ├── titlebar.js        # 标题栏模块
    └── content.js         # 内容区域模块
```

## 开发指南

### 安装依赖

项目已配置使用阿里云NPM镜像，加速依赖下载：

```bash
# 已在.npmrc中配置阿里云镜像源
npm install
```

如果需要手动设置镜像源：

```bash
# 设置npm使用阿里云镜像
npm config set registry https://registry.npmmirror.com
# 设置electron镜像
npm config set ELECTRON_MIRROR https://npmmirror.com/mirrors/electron/
```

### 开发运行

```bash
npm start
```

### 应用打包

```bash
npm run build
```

## 使用说明

1. 启动应用后，将显示一个1920x1080像素的窗口
2. 标题栏可用于拖拽移动窗口
3. 点击右上角的最小化按钮可最小化应用
4. 中间的最大化按钮处于禁用状态
5. 点击右上角的关闭按钮可关闭应用 