<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>海盗王Online - 大坪社区游戏指南</title>
  <link rel="stylesheet" href="styles/main.css">
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
  <div class="app-container">
    <!-- 侧边导航栏 -->
    <nav class="sidebar">
      <div class="sidebar-header">
        <img src="图片/1.jpg" alt="海盗王Logo" class="logo">
        <h1>海盗王Online</h1>
        <p>大坪社区</p>
      </div>
      
      <ul class="nav-menu">
        <li class="nav-item active" data-section="basic">
          <i class="icon">⚙️</i>
          <span>基础设置</span>
        </li>
        <li class="nav-item" data-section="equipment">
          <i class="icon">⚔️</i>
          <span>装备系统</span>
        </li>
        <li class="nav-item" data-section="monsters">
          <i class="icon">👹</i>
          <span>怪物掉落</span>
        </li>
        <li class="nav-item" data-section="dungeons">
          <i class="icon">🏰</i>
          <span>副本介绍</span>
        </li>
        <li class="nav-item" data-section="awakening">
          <i class="icon">✨</i>
          <span>觉醒功能</span>
        </li>
        <li class="nav-item" data-section="demon">
          <i class="icon">😈</i>
          <span>恶魔世界</span>
        </li>
        <li class="nav-item" data-section="island">
          <i class="icon">🏝️</i>
          <span>遗失岛屿</span>
        </li>
      </ul>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
      <div class="content-header">
        <h2 id="section-title">基础详细设置</h2>
        <div class="header-decoration"></div>
      </div>

      <div class="content-body">
        <!-- 基础设置内容 -->
        <section id="basic-content" class="content-section active">
          <div class="feature-grid">
            <div class="feature-card">
              <div class="card-icon">👤</div>
              <h3>角色等级</h3>
              <p>人物最高130级，上线新手报道10级，一代精灵61级，二代精灵81级，人物经验根据角色不同等级调整倍数，爆率2倍。</p>
            </div>
            
            <div class="feature-card">
              <div class="card-icon">🎽</div>
              <h3>装备系统</h3>
              <p>开放披风，背后，腰带首饰，翅膀，坐骑等等。增加酷装与装备分离，并且装备直接可以进行强化，强化幅度增加到Lv40级。</p>
            </div>
            
            <div class="feature-card">
              <div class="card-icon">💎</div>
              <h3>掉落系统</h3>
              <p>怪物掉落增加到20个物品，称号百变，变色系统，变身系统，每日宝箱，击杀积累，赌博系统等等。</p>
            </div>
            
            <div class="feature-card">
              <div class="card-icon">⚙️</div>
              <h3>游戏设置</h3>
              <p>游戏设置选项自带断线重连，分辨率多种选择，60帧画质，特效显示关闭，全屏拾取，PK名字显示，背包自动整理等等。</p>
            </div>
            
            <div class="feature-card">
              <div class="card-icon">💰</div>
              <h3>挂机系统</h3>
              <p>白银城水池挂机，随机获得金钱&水晶奖励，摆摊奖励更加高！</p>
            </div>
            
            <div class="feature-card">
              <div class="card-icon">🎒</div>
              <h3>背包系统</h3>
              <p>本服有超大的背包，198格。随便刷，随便存物品。调整人物的成长属性跟强度，使其更加平衡。</p>
            </div>
            
            <div class="feature-card">
              <div class="card-icon">🎫</div>
              <h3>召唤系统</h3>
              <p>怪物击杀积累到一定数量后，会随机根据角色等级赋予怪物召唤兑换卷。</p>
            </div>
            
            <div class="feature-card">
              <div class="card-icon">💎</div>
              <h3>宝石系统</h3>
              <p>宝石合成，精炼每种宝石3级后开始会失败。</p>
            </div>
          </div>
        </section>

        <!-- 其他内容区域将通过JavaScript动态加载 -->
        <section id="equipment-content" class="content-section"></section>
        <section id="monsters-content" class="content-section"></section>
        <section id="dungeons-content" class="content-section"></section>
        <section id="awakening-content" class="content-section"></section>
        <section id="demon-content" class="content-section"></section>
        <section id="island-content" class="content-section"></section>
      </div>
    </main>
  </div>

  <script src="modules/content.js"></script>
</body>
</html>
