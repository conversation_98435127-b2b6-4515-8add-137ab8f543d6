/**
 * 海盗王Online游戏指南 - 内容管理模块
 *
 * 功能：
 * 1. 处理导航菜单切换
 * 2. 动态加载游戏指南内容
 * 3. 管理内容区域显示
 */

// 游戏指南数据
const gameGuideData = {
  basic: {
    title: '基础详细设置',
    content: `
      <div class="feature-grid">
        <div class="feature-card">
          <div class="card-icon">👤</div>
          <h3>角色等级</h3>
          <p>人物最高130级，上线新手报道10级，一代精灵61级，二代精灵81级，人物经验根据角色不同等级调整倍数，爆率2倍。</p>
        </div>

        <div class="feature-card">
          <div class="card-icon">🎽</div>
          <h3>装备系统</h3>
          <p>开放披风，背后，腰带首饰，翅膀，坐骑等等。增加酷装与装备分离，并且装备直接可以进行强化，强化幅度增加到Lv40级。</p>
        </div>

        <div class="feature-card">
          <div class="card-icon">💎</div>
          <h3>掉落系统</h3>
          <p>怪物掉落增加到20个物品，称号百变，变色系统，变身系统，每日宝箱，击杀积累，赌博系统等等。</p>
        </div>

        <div class="feature-card">
          <div class="card-icon">⚙️</div>
          <h3>游戏设置</h3>
          <p>游戏设置选项自带断线重连，分辨率多种选择，60帧画质，特效显示关闭，全屏拾取，PK名字显示，背包自动整理等等。</p>
        </div>

        <div class="feature-card">
          <div class="card-icon">💰</div>
          <h3>挂机系统</h3>
          <p>白银城水池挂机，随机获得金钱&水晶奖励，摆摊奖励更加高！</p>
        </div>

        <div class="feature-card">
          <div class="card-icon">🎒</div>
          <h3>背包系统</h3>
          <p>本服有超大的背包，198格。随便刷，随便存物品。调整人物的成长属性跟强度，使其更加平衡。</p>
        </div>

        <div class="feature-card">
          <div class="card-icon">🎫</div>
          <h3>召唤系统</h3>
          <p>怪物击杀积累到一定数量后，会随机根据角色等级赋予怪物召唤兑换卷。</p>
        </div>

        <div class="feature-card">
          <div class="card-icon">💎</div>
          <h3>宝石系统</h3>
          <p>宝石合成，精炼每种宝石3级后开始会失败。</p>
        </div>
      </div>
    `
  },
  equipment: {
    title: '装备系统详解',
    content: `
      <div class="equipment-container">
        <div class="equipment-intro">
          <h3>装备升级流程</h3>
          <p>根据海盗王官方装备途径进行逐步提升，35级-45级-55级-65级</p>
        </div>

        <div class="upgrade-flow">
          <div class="upgrade-step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h4>35级 BOSS武器</h4>
              <p>通过35至尊宝箱获得</p>
            </div>
          </div>
          <div class="upgrade-arrow">↓</div>
          <div class="upgrade-step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h4>45级 BOSS武器</h4>
              <p>通过45至尊宝箱获得</p>
            </div>
          </div>
          <div class="upgrade-arrow">↓</div>
          <div class="upgrade-step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h4>55级 BOSS武器</h4>
              <p>通过55至尊宝箱获得</p>
            </div>
          </div>
          <div class="upgrade-arrow">↓</div>
          <div class="upgrade-step">
            <div class="step-number">4</div>
            <div class="step-content">
              <h4>65级 BOSS武器</h4>
              <p>55级BOSS武器 + 65级武器魂魄</p>
            </div>
          </div>
        </div>
      </div>
    `
  },
  monsters: {
    title: '怪物掉落指南',
    content: `
      <div class="monsters-guide">
        <h3>怪物掉落系统</h3>
        <p>本服怪物掉落经过特别调整，提供更丰富的游戏体验。</p>
        <div class="feature-grid">
          <div class="feature-card">
            <div class="card-icon">👹</div>
            <h3>掉落数量</h3>
            <p>每个怪物最多可掉落20个物品，大大增加了收获的乐趣。</p>
          </div>
          <div class="feature-card">
            <div class="card-icon">🎁</div>
            <h3>特殊掉落</h3>
            <p>包含称号、变色道具、变身卡片等特殊物品。</p>
          </div>
        </div>
      </div>
    `
  },
  dungeons: {
    title: '副本介绍',
    content: `
      <div class="dungeons-guide">
        <h3>副本系统</h3>
        <p>丰富的副本内容等待您的挑战。</p>
        <div class="feature-grid">
          <div class="feature-card">
            <div class="card-icon">🏰</div>
            <h3>经典副本</h3>
            <p>保留原版经典副本，提供熟悉的游戏体验。</p>
          </div>
          <div class="feature-card">
            <div class="card-icon">⭐</div>
            <h3>特色副本</h3>
            <p>新增特色副本内容，带来全新挑战。</p>
          </div>
        </div>
      </div>
    `
  },
  awakening: {
    title: '觉醒功能',
    content: `
      <div class="awakening-guide">
        <h3>觉醒系统</h3>
        <p>角色觉醒功能让您的角色获得更强大的能力。</p>
        <div class="feature-grid">
          <div class="feature-card">
            <div class="card-icon">✨</div>
            <h3>觉醒条件</h3>
            <p>达到特定等级和条件后可进行觉醒。</p>
          </div>
          <div class="feature-card">
            <div class="card-icon">🔥</div>
            <h3>觉醒效果</h3>
            <p>觉醒后获得新技能和属性提升。</p>
          </div>
        </div>
      </div>
    `
  },
  demon: {
    title: '恶魔世界',
    content: `
      <div class="demon-guide">
        <h3>恶魔世界</h3>
        <p>充满挑战的恶魔世界等待勇敢的冒险者。</p>
        <div class="feature-grid">
          <div class="feature-card">
            <div class="card-icon">😈</div>
            <h3>恶魔BOSS</h3>
            <p>强大的恶魔BOSS提供丰厚的奖励。</p>
          </div>
          <div class="feature-card">
            <div class="card-icon">🔥</div>
            <h3>特殊奖励</h3>
            <p>恶魔世界独有的珍稀装备和道具。</p>
          </div>
        </div>
      </div>
    `
  },
  island: {
    title: '遗失岛屿',
    content: `
      <div class="island-guide">
        <h3>遗失岛屿</h3>
        <p>神秘的遗失岛屿隐藏着无数宝藏。</p>
        <div class="feature-grid">
          <div class="feature-card">
            <div class="card-icon">🏝️</div>
            <h3>探索奖励</h3>
            <p>探索岛屿可获得稀有材料和装备。</p>
          </div>
          <div class="feature-card">
            <div class="card-icon">🗺️</div>
            <h3>隐藏任务</h3>
            <p>岛屿中隐藏着特殊的任务和挑战。</p>
          </div>
        </div>
      </div>
    `
  }
};

// 导航切换功能
function initNavigation() {
  const navItems = document.querySelectorAll('.nav-item');
  const sectionTitle = document.getElementById('section-title');
  const contentSections = document.querySelectorAll('.content-section');

  navItems.forEach(item => {
    item.addEventListener('click', () => {
      const section = item.dataset.section;

      // 更新导航状态
      navItems.forEach(nav => nav.classList.remove('active'));
      item.classList.add('active');

      // 更新内容
      updateContent(section);
    });
  });
}

// 更新内容区域
function updateContent(section) {
  const sectionTitle = document.getElementById('section-title');
  const contentSections = document.querySelectorAll('.content-section');

  // 隐藏所有内容区域
  contentSections.forEach(sec => sec.classList.remove('active'));

  // 更新标题
  if (gameGuideData[section]) {
    sectionTitle.textContent = gameGuideData[section].title;

    // 显示对应内容
    const targetSection = document.getElementById(`${section}-content`);
    if (targetSection) {
      targetSection.innerHTML = gameGuideData[section].content;
      targetSection.classList.add('active');
    }
  }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
  initNavigation();
  // 默认显示基础设置内容
  updateContent('basic');
});