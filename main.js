/**
 * 海盗王Online游戏指南 - 主进程文件
 *
 * 功能：
 * 1. 创建固定大小的游戏指南窗口(900x550)
 * 2. 设置预加载脚本并启用Node.js集成
 * 3. 管理应用生命周期事件
 */

const { app, BrowserWindow } = require('electron');
const path = require('path');

// 保持对窗口对象的全局引用，避免JavaScript对象被垃圾回收时窗口关闭
let mainWindow;

/**
 * 创建主窗口函数
 */
function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 900,
    height: 550,
    frame: true, // 显示系统标题栏和边框
    resizable: false, // 禁止调整窗口大小
    title: '海盗王Online - 大坪社区游戏指南',
    webPreferences: {
      nodeIntegration: true, // 启用Node.js集成
      contextIsolation: false, // 禁用上下文隔离以简化开发
      enableRemoteModule: false // 禁用remote模块
    }
  });

  // 加载应用的index.html
  mainWindow.loadFile('index.html');

  // 窗口关闭时触发
  mainWindow.on('closed', function () {
    // 取消引用窗口对象
    mainWindow = null;
  });
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(() => {
  createWindow();

  // 在macOS上，当所有窗口都被关闭时，通常会重新创建一个窗口
  app.on('activate', function () {
    if (mainWindow === null) createWindow();
  });
});

// 由于使用系统标题栏，不再需要自定义窗口控制的IPC处理

// 除了macOS外，当所有窗口都被关闭的时候退出程序
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
}); 