/**
 * 标题栏模块 - 处理自定义标题栏的交互行为
 * 
 * 功能：
 * 1. 实现窗口关闭按钮点击事件
 * 2. 实现窗口最小化按钮点击事件
 * 3. 添加禁用状态的最大化按钮
 * 4. 处理标题栏拖拽
 */

// 获取窗口控制按钮
const closeButton = document.getElementById('close-button');
const minimizeButton = document.getElementById('minimize-button');
const maximizeButton = document.getElementById('maximize-button');

// 确保关闭按钮存在后再添加事件监听器
if (closeButton) {
  // 关闭按钮点击事件
  closeButton.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (window.electronAPI && typeof window.electronAPI.closeWindow === 'function') {
      window.electronAPI.closeWindow();
    } else {
      console.error('electronAPI.closeWindow不存在或不是函数');
    }
  });

  // 添加一个额外的鼠标按下事件，防止事件冒泡被拖拽功能干扰
  closeButton.addEventListener('mousedown', (e) => {
    e.stopPropagation();
  });
}

// 确保最小化按钮存在后再添加事件监听器
if (minimizeButton) {
  // 最小化按钮点击事件
  minimizeButton.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (window.electronAPI && typeof window.electronAPI.minimizeWindow === 'function') {
      window.electronAPI.minimizeWindow();
    } else {
      console.error('electronAPI.minimizeWindow不存在或不是函数');
    }
  });

  // 添加一个额外的鼠标按下事件，防止事件冒泡被拖拽功能干扰
  minimizeButton.addEventListener('mousedown', (e) => {
    e.stopPropagation();
  });
}

// 确保最大化按钮存在后再添加事件处理
if (maximizeButton) {
  // 最大化按钮点击事件 - 设置为禁用状态
  maximizeButton.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    // 显示禁用提示
    console.log('窗口最大化功能已禁用');
    
    // 可以在这里添加提示或反馈，例如轻微抖动按钮
    maximizeButton.classList.add('shake');
    setTimeout(() => {
      maximizeButton.classList.remove('shake');
    }, 500);
  });

  // 添加一个额外的鼠标按下事件，防止事件冒泡被拖拽功能干扰
  maximizeButton.addEventListener('mousedown', (e) => {
    e.stopPropagation();
  });
  
  // 添加提示信息
  maximizeButton.title = "最大化已禁用";
}

// 获取标题栏元素
const titleBar = document.querySelector('.title-bar');

// 标题栏点击事件 - 用于拖拽窗口
if (titleBar) {
  titleBar.addEventListener('mousedown', (e) => {
    // 如果点击的不是窗口控制按钮区域，则启用拖拽
    if (!e.target.closest('.window-controls')) {
      window.electronAPI.dragWindow();
    }
  });
} 